"""Main entry point for the phone number data processing application."""

import json
import logging
from pathlib import Path
from typing import Union

from api_client import PhoneNumberAPIClient, HTMLDataParser
from config import (
    INPUT_FILE_1,
    INPUT_FILE_2,
    OUTPUT_FILE,
    LOG_FORMAT,
    LOG_LEVEL,
)
from data_processor import PhoneDataProcessor, DataProcessingError


def setup_logging() -> None:
    """Configure logging for the application."""
    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL),
        format=LOG_FORMAT,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("phone_data_processor.log"),
        ],
    )


class PhoneDataProcessorApp:
    """Main application class for processing phone number data."""

    def __init__(self) -> None:
        """Initialize the application."""
        self.data_processor = PhoneDataProcessor()
        self.html_parser = HTMLDataParser()
        self.logger = logging.getLogger(__name__)

    def load_and_prepare_data(self) -> tuple[str, ...]:
        """Load and prepare data from input files.

        Returns:
            Tuple of unique phone numbers

        Raises:
            DataProcessingError: If data loading or processing fails
        """
        try:
            # Load data files
            first_dataframe = self.data_processor.load_json_data(INPUT_FILE_1)
            second_dataframe = self.data_processor.load_json_data(INPUT_FILE_2)

            # Prepare dataframes
            prepared_df1 = self.data_processor.prepare_dataframe(first_dataframe)
            prepared_df2 = self.data_processor.prepare_dataframe(second_dataframe)

            # Filter and combine columns
            filtered_df1 = self.data_processor.filter_and_combine_columns(
                prepared_df1, "number1", "number2"
            )
            filtered_df2 = self.data_processor.filter_and_combine_columns(
                prepared_df2, "number1", "number2"
            )

            # Normalize phone numbers
            normalized_df1 = self.data_processor.normalize_phone_numbers(filtered_df1)
            normalized_df2 = self.data_processor.normalize_phone_numbers(filtered_df2)

            # Merge dataframes
            merged_dataframe = self.data_processor.merge_dataframes(
                normalized_df1, normalized_df2
            )

            # Get unique phone numbers
            unique_numbers = self.data_processor.get_unique_phone_numbers(
                merged_dataframe
            )

            self.logger.info(f"Prepared {len(unique_numbers)} unique phone numbers")
            return tuple(unique_numbers)

        except Exception as e:
            self.logger.error(f"Data preparation failed: {e}")
            raise DataProcessingError(f"Failed to prepare data: {e}")

    def fetch_api_data(
        self, phone_numbers: tuple[str, ...]
    ) -> dict[str, Union[list[dict], dict]]:
        """Fetch data from API for all phone numbers.

        Args:
            phone_numbers: Tuple of phone numbers to process

        Returns:
            Dictionary of parsed API responses
        """
        if not phone_numbers:
            self.logger.warning("No phone numbers to process")
            return {}

        with PhoneNumberAPIClient() as api_client:
            # Fetch raw API responses
            api_responses = api_client.fetch_multiple_phone_data(list(phone_numbers))

            # Parse HTML responses
            parsed_data = self.html_parser.parse_api_response(api_responses)

        self.logger.info(f"Fetched and parsed data for {len(parsed_data)} numbers")
        return parsed_data

    def save_results(self, results: dict[str, Union[list[dict], dict]]) -> None:
        """Save results to output file.

        Args:
            results: Dictionary of results to save

        Raises:
            DataProcessingError: If file writing fails
        """
        output_path = Path(OUTPUT_FILE)

        try:
            with output_path.open("w", encoding="utf-8") as file:
                json.dump(results, file, indent=2, ensure_ascii=False)

            self.logger.info(f"Results saved to {OUTPUT_FILE}")

        except (OSError, IOError) as e:
            error_msg = f"Failed to save results to {OUTPUT_FILE}: {e}"
            self.logger.error(error_msg)
            raise DataProcessingError(error_msg)

    def run(self) -> None:
        """Run the complete data processing pipeline."""
        try:
            self.logger.info("Starting phone number data processing")

            # Load and prepare data
            phone_numbers = self.load_and_prepare_data()

            if not phone_numbers:
                self.logger.warning("No phone numbers found to process")
                return

            # Fetch API data
            api_results = self.fetch_api_data(phone_numbers)

            # Save results
            self.save_results(api_results)

            self.logger.info("Phone number data processing completed successfully")

        except DataProcessingError:
            # Re-raise data processing errors
            raise
        except Exception as e:
            error_msg = f"Unexpected error during processing: {e}"
            self.logger.error(error_msg)
            raise DataProcessingError(error_msg)


def main() -> None:
    """Main entry point for the application."""
    setup_logging()
    logger = logging.getLogger(__name__)

    try:
        app = PhoneDataProcessorApp()
        app.run()

    except DataProcessingError as e:
        logger.error(f"Data processing failed: {e}")
        exit(1)
    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        exit(130)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        exit(1)


if __name__ == "__main__":
    main()
