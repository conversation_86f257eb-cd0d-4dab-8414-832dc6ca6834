"""API client for fetching and parsing phone number data."""

import logging
from typing import Optional, Union
from concurrent.futures import ThreadPoolExecutor, as_completed

import requests
from bs4 import BeautifulSoup

from config import (
    API_BASE_URL,
    API_TIMEOUT,
    API_ACTION,
    RESULT_CARD_CLASS,
    FIELD_CLASS,
    INFO_LABEL_CLASS,
    INVALID_VALUES,
)

logger = logging.getLogger(__name__)


class APIError(Exception):
    """Custom exception for API-related errors."""

    pass


class PhoneNumberAPIClient:
    """Client for fetching phone number data from external API."""

    def __init__(self, timeout: int = API_TIMEOUT) -> None:
        """Initialize the API client.

        Args:
            timeout: Request timeout in seconds
        """
        self.timeout = timeout
        self.base_url = API_BASE_URL
        self.session = requests.Session()

        # Set default headers
        self.session.headers.update(
            {"User-Agent": "Mozilla/5.0 (compatible; PhoneDataProcessor/1.0)"}
        )

    def _build_api_url(self, phone_number: str) -> str:
        """Build API URL for phone number lookup.

        Args:
            phone_number: Phone number to lookup

        Returns:
            Complete API URL
        """
        return (
            f"{self.base_url}?"
            f"action={API_ACTION}&"
            f"get_number_data=searchdata={phone_number}"
        )

    def fetch_phone_data(self, phone_number: str) -> Optional[dict]:
        """Fetch data from API for a given phone number.

        Args:
            phone_number: Phone number to lookup

        Returns:
            API response data or None if request fails
        """
        if not phone_number or not phone_number.strip():
            logger.warning("Empty phone number provided")
            return None

        api_url = self._build_api_url(phone_number.strip())

        try:
            response = self.session.get(api_url, timeout=self.timeout)
            response.raise_for_status()

            data = response.json()
            logger.debug(f"Successfully fetched data for {phone_number}")
            return data

        except requests.exceptions.Timeout:
            logger.error(f"Timeout fetching data for {phone_number}")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed for {phone_number}: {e}")
            return None
        except ValueError as e:
            logger.error(f"Invalid JSON response for {phone_number}: {e}")
            return None

    def fetch_multiple_phone_data(
        self, phone_numbers: list[str], max_workers: int = 5
    ) -> dict[str, Optional[dict]]:
        """Fetch data for multiple phone numbers concurrently.

        Args:
            phone_numbers: List of phone numbers to lookup
            max_workers: Maximum number of concurrent workers

        Returns:
            Dictionary mapping phone numbers to their API responses
        """
        if not phone_numbers:
            logger.warning("No phone numbers provided")
            return {}

        results = {}

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all requests
            future_to_number = {
                executor.submit(self.fetch_phone_data, number): number
                for number in phone_numbers
            }

            # Collect results as they complete
            for future in as_completed(future_to_number):
                phone_number = future_to_number[future]
                try:
                    result = future.result()
                    results[phone_number] = result
                except Exception as e:
                    logger.error(f"Exception for {phone_number}: {e}")
                    results[phone_number] = None

        logger.info(f"Fetched data for {len(results)} phone numbers")
        return results

    def close(self) -> None:
        """Close the HTTP session."""
        self.session.close()

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()


class HTMLDataParser:
    """Parser for extracting structured data from HTML responses."""

    @staticmethod
    def _normalize_label_text(text: str) -> str:
        """Normalize label text for consistent field names.

        Args:
            text: Raw label text

        Returns:
            Normalized field name
        """
        return text.strip().replace("#", "").replace(" ", "_").lower()

    @staticmethod
    def _normalize_value_text(text: str) -> str:
        """Normalize value text by cleaning whitespace.

        Args:
            text: Raw value text

        Returns:
            Cleaned value text
        """
        return " ".join(text.strip().split())

    @staticmethod
    def _is_valid_value(value: str) -> bool:
        """Check if value is valid (not empty or placeholder).

        Args:
            value: Value to validate

        Returns:
            True if value is valid, False otherwise
        """
        return value.lower() not in INVALID_VALUES

    def parse_result_card(self, card: BeautifulSoup) -> dict[str, str]:
        """Parse a single result card from HTML content.

        Args:
            card: BeautifulSoup object representing a result card

        Returns:
            Dictionary of extracted field data
        """
        record = {}

        for field in card.find_all("div", class_=FIELD_CLASS):
            label_element = field.find("label", class_=INFO_LABEL_CLASS)
            value_element = field.find("div")

            # Skip if required elements are missing
            if not label_element or not value_element:
                continue

            label_text = self._normalize_label_text(label_element.get_text())
            value_text = self._normalize_value_text(value_element.get_text())

            # Only include valid values
            if self._is_valid_value(value_text):
                record[label_text] = value_text

        return record

    def parse_api_response(
        self, api_results: dict[str, Optional[dict]]
    ) -> dict[str, Union[list[dict], dict]]:
        """Parse API responses and extract structured data.

        Args:
            api_results: Dictionary of phone numbers to API responses

        Returns:
            Dictionary of phone numbers to parsed data or error information
        """
        parsed_results = {}

        for phone_number, response in api_results.items():
            if response is None:
                parsed_results[phone_number] = {"error": "No response received"}
                continue

            if not response.get("success"):
                parsed_results[phone_number] = {"error": "API request failed"}
                continue

            try:
                soup = BeautifulSoup(response["data"], "html.parser")
                result_cards = soup.find_all("div", class_=RESULT_CARD_CLASS)

                records = [self.parse_result_card(card) for card in result_cards]

                # Filter out empty records
                valid_records = [record for record in records if record]
                parsed_results[phone_number] = valid_records

            except Exception as e:
                logger.error(f"Error parsing response for {phone_number}: {e}")
                parsed_results[phone_number] = {"error": f"Parsing failed: {e}"}

        logger.info(f"Parsed responses for {len(parsed_results)} phone numbers")
        return parsed_results
