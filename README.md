# Phone Number Data Processor

A refactored Python application for processing phone number data from JSON files and fetching additional information from external APIs.

## Overview

This application has been completely refactored following strict Python coding principles to ensure:
- Single Responsibility Principle (SRP)
- DRY (Don't Repeat Yourself)
- Proper error handling and logging
- Type safety with comprehensive type hints
- Immutable data structures where possible
- Context managers for resource management

## Architecture

The application is now modularized into focused components:

### Core Modules

- **`main.py`** - Main entry point and orchestration
- **`data_processor.py`** - Data loading and processing logic
- **`api_client.py`** - API interaction and HTML parsing
- **`config.py`** - Configuration constants

### Key Classes

- **`PhoneDataProcessor`** - Handles JSON data loading and phone number processing
- **`PhoneNumberAPIClient`** - Manages API requests with connection pooling
- **`HTMLDataParser`** - Parses HTML responses into structured data
- **`PhoneDataProcessorApp`** - Main application orchestrator

## Features

- ✅ **Multi-File Processing**: Automatically discovers and processes any number of files
- ✅ **Multiple File Formats**: Supports JSON, CSV, and Excel files
- ✅ **Flexible File Naming**: No naming format requirements - any filename works
- ✅ **Modular Design**: Separated concerns into focused classes
- ✅ **Error Handling**: Custom exceptions with detailed error messages
- ✅ **Graceful Degradation**: Skips invalid files and continues processing
- ✅ **Logging**: Structured logging with file and console output
- ✅ **Type Safety**: Comprehensive type hints throughout
- ✅ **Resource Management**: Context managers for API clients and file operations
- ✅ **Concurrent Processing**: Thread-based concurrent API requests
- ✅ **Data Validation**: Input validation and error recovery
- ✅ **Immutable Approach**: Avoids in-place modifications where possible

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage

Run the main application:
```bash
python main.py
```

## Configuration

All configuration is centralized in `config.py`:

- **File paths**: Input/output file locations
- **API settings**: URLs, timeouts, and request parameters
- **Data processing**: Column mappings and validation rules
- **Logging**: Format and level configuration

## Input Files

The application automatically discovers and processes **any number of data files** from the `data/` directory.

### Supported File Formats
- **JSON** (`.json`)
- **CSV** (`.csv`)

### Requirements
- Files must contain columns: `_ao3e` and `x1iyjqo2`
- Files without required columns are automatically skipped with a warning
- **No specific naming format required** - any filename works!

### Example Directory Structure
```
data/
├── customer_data.json
├── phone_numbers.csv
├── any_name_you_want.json
└── more_data.csv
```

## Output

Results are saved to `output_v0.json` with structured data for each phone number.

## Testing

Run the basic test suite:
```bash
python test_refactored_code.py
```

For comprehensive testing with pytest:
```bash
pip install pytest
pytest test_refactored_code.py -v
```

## Logging

Logs are written to:
- Console (INFO level and above)
- `phone_data_processor.log` file (all levels)

## Error Handling

The application includes comprehensive error handling:
- **File not found errors**
- **Network timeouts and failures**
- **Invalid JSON data**
- **Missing required columns**
- **API response parsing errors**

## Performance Features

- **Concurrent API requests** using ThreadPoolExecutor
- **Connection pooling** with requests.Session
- **Lazy evaluation** where appropriate
- **Memory-efficient data processing**

## Code Quality

The refactored code follows strict Python principles:
- **PEP 8** compliance
- **Single Responsibility Principle**
- **DRY principle**
- **Early returns** to reduce nesting
- **Descriptive naming** throughout
- **Type hints** for all public APIs
- **Context managers** for resource management
- **Custom exceptions** with clear messages

## Migration from Legacy Code

The original `app.py` has been refactored into:
1. **Data processing logic** → `data_processor.py` (now supports multiple file formats)
2. **API interaction** → `api_client.py`
3. **Configuration** → `config.py` (now supports dynamic file discovery)
4. **Main orchestration** → `main.py` (now processes entire directories)
5. **Testing** → `test_multi_file_processing.py`

### Key Improvements
- **From 2 hardcoded files** → **Any number of files in data/ directory**
- **From JSON only** → **JSON, CSV, and Excel support**
- **From fixed filenames** → **Any filename works**
- **From rigid structure** → **Graceful handling of invalid files**

## Dependencies

- **pandas**: Data manipulation and analysis
- **requests**: HTTP library for API calls
- **beautifulsoup4**: HTML parsing
- **lxml**: Fast XML/HTML parser

## Contributing

When contributing, ensure code follows the established patterns:
1. Use type hints for all functions
2. Follow the Single Responsibility Principle
3. Add comprehensive error handling
4. Include logging for important operations
5. Write tests for new functionality
6. Use context managers for resources
