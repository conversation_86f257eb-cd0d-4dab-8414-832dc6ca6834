"""Configuration constants for the data processing application."""

from typing import Final

# Directory paths
DATA_DIRECTORY: Final[str] = "data"
OUTPUT_FILE: Final[str] = "output.json"

# Supported file extensions for auto-discovery
SUPPORTED_EXTENSIONS: Final[frozenset[str]] = frozenset({".json", ".csv"})

# Data processing constants
COLUMNS_TO_KEEP: Final[tuple[str, ...]] = ("_ao3e", "x1iyjqo2")
COLUMN_MAPPING: Final[dict[str, str]] = {"_ao3e": "number1", "x1iyjqo2": "number2"}
PHONE_PREFIX: Final[str] = "+92"
REPLACEMENT_PREFIX: Final[str] = "0"
COMBINED_COLUMN_NAME: Final[str] = "combined_column"

# API configuration
API_BASE_URL: Final[str] = "https://simownerdetails.net.pk/wp-admin/admin-ajax.php"
API_TIMEOUT: Final[int] = 10
API_ACTION: Final[str] = "get_number_data"

# HTML parsing constants
RESULT_CARD_CLASS: Final[str] = "result-card"
FIELD_CLASS: Final[str] = "field"
INFO_LABEL_CLASS: Final[str] = "info"

# Data validation constants
INVALID_VALUES: Final[frozenset[str]] = frozenset({"", "unknown", "record not found"})

# Logging configuration
LOG_FORMAT: Final[str] = (
    "%(filename)s:%(lineno)d - %(asctime)s - %(levelname)s - %(message)s"
)
LOG_LEVEL: Final[str] = "INFO"
