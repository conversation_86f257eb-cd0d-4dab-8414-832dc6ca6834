"""Data processing utilities for phone number data."""

import logging
from pathlib import Path

import pandas as pd

from config import (
    COLUMNS_TO_KEEP,
    COLUMN_MAPPING,
    PHONE_PREFIX,
    REPLACEMENT_PREFIX,
    COMBINED_COLUMN_NAME,
    DATA_DIRECTORY,
    SUPPORTED_EXTENSIONS,
)

logger = logging.getLogger(__name__)


class DataProcessingError(Exception):
    """Custom exception for data processing errors."""

    pass


class PhoneDataProcessor:
    """Handles loading and processing of phone number data from JSON files."""

    def __init__(self) -> None:
        """Initialize the data processor."""
        self._validate_configuration()

    def _validate_configuration(self) -> None:
        """Validate configuration constants."""
        if not COLUMNS_TO_KEEP:
            raise DataProcessingError("COLUMNS_TO_KEEP cannot be empty")
        if not COLUMN_MAPPING:
            raise DataProcessingError("COLUMN_MAPPING cannot be empty")
        if not PHONE_PREFIX:
            raise DataProcessingError("PHONE_PREFIX cannot be empty")

    def discover_data_files(self, directory: str = DATA_DIRECTORY) -> list[Path]:
        """Discover all supported data files in the specified directory.

        Args:
            directory: Directory to search for data files

        Returns:
            List of Path objects for discovered files

        Raises:
            DataProcessingError: If directory doesn't exist or no files found
        """
        data_dir = Path(directory)
        if not data_dir.exists():
            raise DataProcessingError(f"Data directory not found: {directory}")

        if not data_dir.is_dir():
            raise DataProcessingError(f"Path is not a directory: {directory}")

        discovered_files = []
        for extension in SUPPORTED_EXTENSIONS:
            discovered_files.extend(data_dir.glob(f"*{extension}"))

        if not discovered_files:
            raise DataProcessingError(
                f"No supported data files found in {directory}. "
                f"Supported extensions: {', '.join(SUPPORTED_EXTENSIONS)}"
            )

        # Sort files for consistent processing order
        discovered_files.sort()
        logger.info(f"Discovered {len(discovered_files)} data files in {directory}")

        return discovered_files

    def load_data_file(self, file_path: Path) -> pd.DataFrame:
        """Load data from a file based on its extension.

        Args:
            file_path: Path to the data file

        Returns:
            Loaded DataFrame

        Raises:
            DataProcessingError: If file cannot be loaded or is invalid
        """
        if not file_path.exists():
            raise DataProcessingError(f"File not found: {file_path}")

        extension = file_path.suffix.lower()

        try:
            if extension == ".json":
                dataframe = pd.read_json(file_path)
            elif extension == ".csv":
                dataframe = pd.read_csv(file_path)
            else:
                raise DataProcessingError(f"Unsupported file extension: {extension}")

            logger.info(f"Loaded {len(dataframe)} records from {file_path.name}")
            return dataframe

        except Exception as e:
            raise DataProcessingError(f"Failed to load file {file_path}: {e}")

    def load_json_data(self, file_path: str) -> pd.DataFrame:
        """Load and validate JSON data from file (legacy method).

        Args:
            file_path: Path to the JSON file

        Returns:
            Loaded DataFrame

        Raises:
            DataProcessingError: If file cannot be loaded or is invalid
        """
        return self.load_data_file(Path(file_path))

    def _validate_required_columns(self, dataframe: pd.DataFrame) -> None:
        """Validate that required columns exist in the DataFrame."""
        missing_columns = set(COLUMNS_TO_KEEP) - set(dataframe.columns)
        if missing_columns:
            raise DataProcessingError(f"Missing required columns: {missing_columns}")

    def prepare_dataframe(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """Prepare DataFrame by selecting and renaming columns.

        Args:
            dataframe: Input DataFrame

        Returns:
            Processed DataFrame with selected and renamed columns

        Raises:
            DataProcessingError: If required columns are missing
        """
        self._validate_required_columns(dataframe)

        # Select required columns (immutable approach)
        selected_df = dataframe[list(COLUMNS_TO_KEEP)].copy()

        # Rename columns
        renamed_df = selected_df.rename(columns=COLUMN_MAPPING)

        logger.info(f"Prepared DataFrame with columns: {list(renamed_df.columns)}")
        return renamed_df

    def filter_and_combine_columns(
        self, dataframe: pd.DataFrame, col1: str, col2: str, prefix: str = PHONE_PREFIX
    ) -> pd.DataFrame:
        """Filter columns by prefix and combine them.

        Args:
            dataframe: Input DataFrame
            col1: First column name
            col2: Second column name
            prefix: Prefix to filter by

        Returns:
            DataFrame with combined filtered data

        Raises:
            DataProcessingError: If columns don't exist
        """
        if col1 not in dataframe.columns or col2 not in dataframe.columns:
            raise DataProcessingError(f"Columns {col1} or {col2} not found")

        # Convert to string and handle NaN values
        col1_str = dataframe[col1].astype(str).replace("nan", "")
        col2_str = dataframe[col2].astype(str).replace("nan", "")

        # Filter columns by prefix
        filtered_col1 = col1_str[col1_str.str.startswith(prefix, na=False)]
        filtered_col2 = col2_str[col2_str.str.startswith(prefix, na=False)]

        # Combine filtered data
        combined_series = pd.concat([filtered_col1, filtered_col2], ignore_index=True)

        result_df = pd.DataFrame({COMBINED_COLUMN_NAME: combined_series})
        logger.info(f"Combined {len(result_df)} phone numbers with prefix {prefix}")

        return result_df

    def normalize_phone_numbers(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """Normalize phone numbers by replacing prefix and removing spaces.

        Args:
            dataframe: DataFrame with phone numbers

        Returns:
            DataFrame with normalized phone numbers
        """
        if COMBINED_COLUMN_NAME not in dataframe.columns:
            raise DataProcessingError(f"Column {COMBINED_COLUMN_NAME} not found")

        # Create a copy to avoid modifying the original
        normalized_df = dataframe.copy()

        # Convert to string and normalize
        normalized_df[COMBINED_COLUMN_NAME] = (
            normalized_df[COMBINED_COLUMN_NAME]
            .astype(str)
            .str.replace(PHONE_PREFIX, REPLACEMENT_PREFIX, regex=False)
            .str.replace(" ", "", regex=False)
        )

        logger.info(f"Normalized {len(normalized_df)} phone numbers")
        return normalized_df

    def merge_dataframes(
        self,
        df1: pd.DataFrame,
        df2: pd.DataFrame,
        on_column: str = COMBINED_COLUMN_NAME,
    ) -> pd.DataFrame:
        """Merge two DataFrames on specified column.

        Args:
            df1: First DataFrame
            df2: Second DataFrame
            on_column: Column to merge on

        Returns:
            Merged DataFrame

        Raises:
            DataProcessingError: If merge column doesn't exist
        """
        if on_column not in df1.columns or on_column not in df2.columns:
            raise DataProcessingError(f"Merge column {on_column} not found")

        merged_df = pd.merge(df1, df2, on=on_column, how="outer")
        logger.info(f"Merged DataFrames: {len(merged_df)} total records")

        return merged_df

    def get_unique_phone_numbers(self, dataframe: pd.DataFrame) -> list[str]:
        """Extract unique phone numbers from DataFrame.

        Args:
            dataframe: DataFrame containing phone numbers

        Returns:
            List of unique phone numbers
        """
        if COMBINED_COLUMN_NAME not in dataframe.columns:
            raise DataProcessingError(f"Column {COMBINED_COLUMN_NAME} not found")

        unique_numbers = dataframe[COMBINED_COLUMN_NAME].unique().tolist()
        logger.info(f"Found {len(unique_numbers)} unique phone numbers")

        return unique_numbers

    def process_multiple_files(self, directory: str = DATA_DIRECTORY) -> pd.DataFrame:
        """Process all data files in directory and return merged result.

        Args:
            directory: Directory containing data files

        Returns:
            Merged DataFrame with all processed phone numbers

        Raises:
            DataProcessingError: If processing fails
        """
        # Discover all data files
        data_files = self.discover_data_files(directory)

        processed_dataframes = []

        for file_path in data_files:
            try:
                # Load and prepare each file
                raw_dataframe = self.load_data_file(file_path)

                # Check if file has required columns, skip if not
                if not set(COLUMNS_TO_KEEP).issubset(set(raw_dataframe.columns)):
                    logger.warning(
                        f"Skipping {file_path.name}: missing required columns "
                        f"{COLUMNS_TO_KEEP}. Available columns: {list(raw_dataframe.columns)}"
                    )
                    continue

                prepared_df = self.prepare_dataframe(raw_dataframe)

                # Filter and combine columns
                filtered_df = self.filter_and_combine_columns(
                    prepared_df, "number1", "number2"
                )

                # Normalize phone numbers
                normalized_df = self.normalize_phone_numbers(filtered_df)

                processed_dataframes.append(normalized_df)
                logger.info(f"Successfully processed {file_path.name}")

            except Exception as e:
                logger.error(f"Failed to process {file_path.name}: {e}")
                # Continue processing other files instead of failing completely
                continue

        if not processed_dataframes:
            raise DataProcessingError("No files could be processed successfully")

        # Combine all processed dataframes
        if len(processed_dataframes) == 1:
            final_dataframe = processed_dataframes[0]
        else:
            # Merge all dataframes using outer join
            final_dataframe = processed_dataframes[0]
            for df in processed_dataframes[1:]:
                final_dataframe = self.merge_dataframes(final_dataframe, df)

        logger.info(
            f"Successfully processed {len(processed_dataframes)} files, "
            f"resulting in {len(final_dataframe)} total records"
        )

        return final_dataframe
